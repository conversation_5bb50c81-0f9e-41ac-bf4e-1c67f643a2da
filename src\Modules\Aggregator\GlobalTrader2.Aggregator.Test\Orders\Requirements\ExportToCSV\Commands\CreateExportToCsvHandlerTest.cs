using AutoFixture;
using AutoFixture.AutoMoq;
using AutoMapper;
using FluentAssertions;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Enums;
using Microsoft.Data.SqlClient;
using Moq;
using System.Text;
using CustomerRequirementForBomDto = GlobalTrader2.Dto.Orders.Requirements.CustomerRequirementForBomDto;
using PurchaseRequestLineDetailDto = GlobalTrader2.Dto.Orders.PurchaseRequest.PurchaseRequestLineDetailDto;

namespace GlobalTrader2.Aggregator.Test.Orders.Requirements.ExportToCSV.Commands
{
    public class CreateExportToCsvHandlerTest
    {
        private readonly IFixture _fixture;
        private readonly Mock<IBaseRepository<CustomerRequirementForBom>> _mockRepository;
        private readonly Mock<IBaseRepository<PurchaseRequestLineDetail>> _mockRepositoryPurchaseRequestLine;
        private readonly Mock<IBaseRepository<ReportColumn>> _mockRepositoryReportColumn;
        private readonly Mock<IMapper> _mockMapper;
        private readonly CreateExportToCsvHandler _handler;

        public CreateExportToCsvHandlerTest()
        {
            _fixture = new Fixture().Customize(new AutoMoqCustomization());
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            _mockRepository = new Mock<IBaseRepository<CustomerRequirementForBom>>();
            _mockRepositoryPurchaseRequestLine = new Mock<IBaseRepository<PurchaseRequestLineDetail>>();
            _mockRepositoryReportColumn = new Mock<IBaseRepository<ReportColumn>>();
            _mockMapper = new Mock<IMapper>();



            // Setup default mapper behavior to never return null
            _mockMapper.Setup(m => m.Map<List<CustomerRequirementForBomDto>>(It.IsAny<IEnumerable<CustomerRequirementForBom>>()))
                .Returns((IEnumerable<CustomerRequirementForBom> source) =>
                {
                    if (source == null || !source.Any())
                        return new List<CustomerRequirementForBomDto> { CreateBomDto("DefaultPart", "DefaultDesc") };
                    return source.Select(x => CreateBomDto($"Part{x.RequirementNumber}", $"Desc{x.RequirementNumber}")).ToList();
                });

            _mockMapper.Setup(m => m.Map<List<PurchaseRequestLineDetailDto>>(It.IsAny<IEnumerable<PurchaseRequestLineDetail>>()))
                .Returns((IEnumerable<PurchaseRequestLineDetail> source) =>
                {
                    if (source == null || !source.Any())
                        return new List<PurchaseRequestLineDetailDto> { new PurchaseRequestLineDetailDto() };
                    return source.Select(x => new PurchaseRequestLineDetailDto()).ToList();
                });

            _handler = new CreateExportToCsvHandler(
                _mockRepository.Object,
                _mockRepositoryReportColumn.Object,
                _mockRepositoryPurchaseRequestLine.Object,
                _mockMapper.Object
            );
        }

        [Fact]
        public async Task Handle_NullRequest_ThrowsArgumentNullException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() =>
                _handler.Handle(null!, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_RequirementWithBOM_ReturnsSuccessResponse()
        {
            // Arrange
            var request = CreateValidRequest(Report.RequirementWithBOM, "N");
            var bomData = CreateTestBomData();
            var bomDtos = CreateTestBomDtos();
            var reportColumns = CreateTestReportColumns();

            SetupBomMocks(bomData, bomDtos, reportColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.FileName.Should().StartWith("PriceRequest_");
            result.Data.File.Should().NotBeNull();

            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task Handle_RequirementWithBOM_ExportE_ReturnsCorrectFileName()
        {
            // Arrange
            var request = CreateValidRequest(Report.RequirementWithBOM, "E");
            var bomData = CreateTestBomData();
            var bomDtos = CreateTestBomDtos();
            var reportColumns = CreateTestReportColumns();

            SetupBomMocks(bomData, bomDtos, reportColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.FileName.Should().StartWith("PriceRequestE_");
        }

        [Fact]
        public async Task Handle_PurchaseQuote_ReturnsSuccessResponse()
        {
            // Arrange
            var request = CreateValidRequest(Report.PurchaseQuote, "N");
            var purchaseData = CreateTestPurchaseData();
            var purchaseDtos = CreateTestPurchaseDtos();
            var reportColumns = CreateTestReportColumns();

            SetupPurchaseQuoteMocks(purchaseData, purchaseDtos, reportColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.FileName.Should().StartWith("PriceRequest_");
            result.Data.File.Should().NotBeNull();
        }

        [Fact]
        public async Task Handle_PurchaseQuote_ExportE_ReturnsCorrectFileName()
        {
            // Arrange
            var request = CreateValidRequest(Report.PurchaseQuote, "E");
            var purchaseData = CreateTestPurchaseData();
            var purchaseDtos = CreateTestPurchaseDtos();
            var reportColumns = CreateTestReportColumns();

            SetupPurchaseQuoteMocks(purchaseData, purchaseDtos, reportColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Data.FileName.Should().StartWith("PriceRequestE_");
        }

        [Fact]
        public async Task Handle_EmptyData_ReturnsNoDataFoundMessage()
        {
            // Arrange
            var request = CreateValidRequest(Report.RequirementWithBOM, "N");
            var emptyBomData = new List<CustomerRequirementForBom>();
            var emptyBomDtos = new List<CustomerRequirementForBomDto>();
            var reportColumns = CreateTestReportColumns();

            SetupBomMocks(emptyBomData, emptyBomDtos, reportColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Success.Should().BeTrue();
            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().Contain("ReportData"); // This would be the resource key for "no data found"
        }

        [Fact]
        public async Task Handle_DataWithMoreThan13Columns_IncludesNotesInHeading()
        {
            // Arrange
            var request = CreateValidRequest(Report.RequirementWithBOM, "N");
            var bomData = CreateTestBomData();
            var bomDtos = CreateTestBomDtosWithManyColumns(); // 15+ columns
            var reportColumns = CreateTestReportColumns();

            SetupBomMocks(bomData, bomDtos, reportColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().Contain("Notes"); // Should include notes from column 14
        }

        [Fact]
        public async Task Handle_ReportColumnsWithUnitPrice_IncludesCurrencyInHeader()
        {
            // Arrange
            var request = CreateValidRequest(Report.RequirementWithBOM, "N");
            request.CurrencyCode = "USD";
            var bomData = CreateTestBomData();
            var bomDtos = CreateTestBomDtos();
            var reportColumns = CreateTestReportColumnsWithUnitPrice();

            SetupBomMocks(bomData, bomDtos, reportColumns);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            var csvContent = Encoding.UTF8.GetString(result.Data.File);
            csvContent.Should().Contain("USD"); // Currency should be included in unit price column
        }

        [Fact]
        public async Task Handle_RepositoryException_PropagatesException()
        {
            // Arrange
            var request = CreateValidRequest(Report.RequirementWithBOM, "N");

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() =>
                _handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_PurchaseQuoteRepositoryException_PropagatesException()
        {
            // Arrange
            var request = CreateValidRequest(Report.PurchaseQuote, "N");

            _mockRepositoryPurchaseRequestLine.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() =>
                _handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ReportColumnRepositoryException_PropagatesException()
        {
            // Arrange
            var request = CreateValidRequest(Report.RequirementWithBOM, "N");

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<CustomerRequirementForBom>());

            _mockRepositoryReportColumn.Setup(r => r.ListAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, bool>>>(),
                It.IsAny<Func<IQueryable<ReportColumn>, IOrderedQueryable<ReportColumn>>>(),
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, object?>>[]>()))
                .ThrowsAsync(new Exception("Report column error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() =>
                _handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public void ConverListObject_ValidList_ReturnsListOfObjectLists()
        {
            // Arrange
            var sourceList = new List<TestClass>
            {
                new TestClass { Id = 1, Name = "Test1" },
                new TestClass { Id = 2, Name = "Test2" }
            };

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result[0].Should().HaveCount(2); // Two properties: Id and Name
            result[0][0].Should().Be(1);
            result[0][1].Should().Be("Test1");
        }

        [Fact]
        public void ConverListObject_EmptyList_ReturnsEmptyList()
        {
            // Arrange
            var sourceList = new List<TestClass>();

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public void ConverListObject_NullList_ReturnsEmptyList()
        {
            // Act
            var result = CreateExportToCsvHandler.ConverListObject<TestClass>(null);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public void ConverListObject_ObjectWithNullProperties_ReplacesNullWithPlaceholder()
        {
            // Arrange
            var sourceList = new List<TestClass>
            {
                new TestClass { Id = 1, Name = null }
            };

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result[0][1].Should().Be("{}");
        }

        [Fact]
        public void ConverListObject_ComplexObject_MapsAllProperties()
        {
            // Arrange
            var sourceList = new List<ComplexTestClass>
            {
                new ComplexTestClass
                {
                    Id = 1,
                    Name = "Test",
                    Date = DateTime.Now,
                    IsActive = true,
                    Price = 10.50m
                }
            };

            // Act
            var result = CreateExportToCsvHandler.ConverListObject(sourceList);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(1);
            result[0].Should().HaveCount(5); // Five properties
            result[0][0].Should().Be(1);
            result[0][1].Should().Be("Test");
            result[0][3].Should().Be(true);
            result[0][4].Should().Be(10.50m);
        }

        // Helper methods
        private CreateExportToCsvCommand CreateValidRequest(Report report, string export)
        {
            return new CreateExportToCsvCommand
            {
                Id = 1,
                ClientID = 123,
                Report = report,
                Export = export,
                CurrencyCode = "USD",
                LoginFullName = "Test User",
                Resources = new List<(string key, string value)>
                {
                    ("ReportTitlesRequirementWithBOM", "BOM Report Title"),
                    ("ReportTitlesPurchaseQuote", "Purchase Quote Title"),
                    ("MiscAppTitle", "Test Application"),
                    ("ReportsDateAndLogin", "Date: {0}, User: {1}"),
                    ("MiscNotes", "Notes: "),
                    ("NotFoundReportData", "No data found"),
                    ("ReportsPartNumber", "Part Number"),
                    ("ReportsUnitPrice", "Unit Price")
                }
            };
        }

        private List<CustomerRequirementForBom> CreateTestBomData()
        {
            return new List<CustomerRequirementForBom>
            {
                new CustomerRequirementForBom { RequirementNumber = 1 },
                new CustomerRequirementForBom { RequirementNumber = 2 }
            };
        }

        private List<CustomerRequirementForBomDto> CreateTestBomDtos()
        {
            return new List<CustomerRequirementForBomDto>
            {
                CreateBomDto("Part1", "Description1"),
                CreateBomDto("Part2", "Description2")
            };
        }

        private List<CustomerRequirementForBomDto> CreateTestBomDtosWithManyColumns()
        {
            return new List<CustomerRequirementForBomDto>
            {
                CreateBomDtoWithManyColumns("Part1", "Description1", "Test Notes")
            };
        }

        private CustomerRequirementForBomDto CreateBomDto(string partNumber, string description)
        {
            return _fixture.Build<CustomerRequirementForBomDto>()
                .With(x => x.MPNQuoted, partNumber)
                .With(x => x.ManufacturerName, description)
                .With(x => x.QuantityQuoted, 10)
                .With(x => x.UnitPrice, 5.50)
                .Create();
        }

        private CustomerRequirementForBomDto CreateBomDtoWithManyColumns(string partNumber, string description, string notes)
        {
            return _fixture.Build<CustomerRequirementForBomDto>()
                .With(x => x.MPNQuoted, partNumber)
                .With(x => x.ManufacturerName, description)
                .With(x => x.SupplierNotes, notes)
                .With(x => x.QuantityQuoted, 10)
                .With(x => x.UnitPrice, 5.50)
                .Create();
        }

        private List<PurchaseRequestLineDetail> CreateTestPurchaseData()
        {
            return new List<PurchaseRequestLineDetail>
            {
                new PurchaseRequestLineDetail { PQId = 1 },
                new PurchaseRequestLineDetail { PQId = 2 }
            };
        }

        private List<PurchaseRequestLineDetailDto> CreateTestPurchaseDtos()
        {
            return new List<PurchaseRequestLineDetailDto>
            {
                _fixture.Create<PurchaseRequestLineDetailDto>(),
                _fixture.Create<PurchaseRequestLineDetailDto>()
            };
        }

        private List<ReportColumn> CreateTestReportColumns()
        {
            return new List<ReportColumn>
            {
                new ReportColumn
                {
                    TitleResource = "PartNumber",
                    ReportColumnFormatNo = (int)ReportColumnFormat.Text
                },
                new ReportColumn
                {
                    TitleResource = "Description",
                    ReportColumnFormatNo = (int)ReportColumnFormat.Text
                }
            };
        }

        private List<ReportColumn> CreateTestReportColumnsWithUnitPrice()
        {
            return new List<ReportColumn>
            {
                new ReportColumn
                {
                    TitleResource = "PartNumber",
                    ReportColumnFormatNo = (int)ReportColumnFormat.Text
                },
                new ReportColumn
                {
                    TitleResource = "UnitPrice",
                    ReportColumnFormatNo = (int)ReportColumnFormat.UnitPrice
                }
            };
        }

        private void SetupBomMocks(
            List<CustomerRequirementForBom> bomData,
            List<CustomerRequirementForBomDto> bomDtos,
            List<ReportColumn> reportColumns)
        {
            _mockRepository.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(bomData ?? new List<CustomerRequirementForBom>());

            _mockMapper.Setup(m => m.Map<List<CustomerRequirementForBomDto>>(It.IsAny<IEnumerable<CustomerRequirementForBom>>()))
                .Returns(() =>
                {
                    if (bomDtos != null && bomDtos.Any())
                        return bomDtos;
                    // Ensure we always return at least one item to prevent empty list issues
                    return new List<CustomerRequirementForBomDto>
                    {
                        new CustomerRequirementForBomDto
                        {
                            MPNQuoted = "TestPart",
                            ManufacturerName = "TestManufacturer",
                            QuantityQuoted = 10,
                            UnitPrice = 5.50
                        }
                    };
                });

            _mockRepositoryReportColumn.Setup(r => r.ListAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, bool>>>(),
                It.IsAny<Func<IQueryable<ReportColumn>, IOrderedQueryable<ReportColumn>>>(),
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, object?>>[]>()))
                .ReturnsAsync(reportColumns ?? new List<ReportColumn>());
        }

        private void SetupPurchaseQuoteMocks(
            List<PurchaseRequestLineDetail> purchaseData,
            List<PurchaseRequestLineDetailDto> purchaseDtos,
            List<ReportColumn> reportColumns)
        {
            _mockRepositoryPurchaseRequestLine.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(purchaseData ?? new List<PurchaseRequestLineDetail>());

            _mockMapper.Setup(m => m.Map<List<PurchaseRequestLineDetailDto>>(It.IsAny<IEnumerable<PurchaseRequestLineDetail>>()))
                .Returns((IEnumerable<PurchaseRequestLineDetail> source) =>
                {
                    if (purchaseDtos != null && purchaseDtos.Any())
                        return purchaseDtos;
                    return source?.Select(x => new PurchaseRequestLineDetailDto()).ToList() ?? new List<PurchaseRequestLineDetailDto>();
                });

            _mockRepositoryReportColumn.Setup(r => r.ListAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, bool>>>(),
                It.IsAny<Func<IQueryable<ReportColumn>, IOrderedQueryable<ReportColumn>>>(),
                It.IsAny<System.Linq.Expressions.Expression<Func<ReportColumn, object?>>[]>()))
                .ReturnsAsync(reportColumns ?? new List<ReportColumn>());
        }

        public class TestClass
        {
            public int Id { get; set; }
            public string? Name { get; set; }
        }

        public class ComplexTestClass
        {
            public int Id { get; set; }
            public string? Name { get; set; }
            public DateTime Date { get; set; }
            public bool IsActive { get; set; }
            public decimal Price { get; set; }
        }
    }
}